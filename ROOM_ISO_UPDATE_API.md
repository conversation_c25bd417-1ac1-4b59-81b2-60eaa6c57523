# Bulk Room ISO Code Update API

## Overview
This API endpoint provides fast bulk updating of ISO codes for shipment rooms by matching room names with the `room_iso_codes` table.

## Endpoint
```
POST /admin/room/bulk-update-iso-codes
```

## Authentication
Requires valid JWT token in Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Request
No request body required. The API automatically:
1. Matches `shipment_rooms.name` with `room_iso_codes.name`
2. Updates `shipment_rooms.iso_code` with `room_iso_codes.code` for exact matches
3. Sets `shipment_rooms.iso_code` to '999' for rooms without matching ISO codes
4. Uses raw SQL for maximum performance with large datasets

## Response
```json
{
  "status": 1,
  "message": "Successfully updated ISO codes for 150 shipment rooms. Rooms with matching names got their ISO codes, others got '999' as default.",
  "data": {
    "updated_count": 150,
    "statistics": {
      "before_update": {
        "total_shipment_rooms": 500,
        "rooms_with_iso_code": 200,
        "rooms_with_matching_iso_code": 300
      },
      "after_update": {
        "total_shipment_rooms": 500,
        "rooms_with_iso_code": 500,
        "rooms_with_matching_iso_code": 300
      }
    }
  }
}
```

## Performance Features
- Uses raw SQL `UPDATE ... INNER JOIN` for maximum speed
- Single database operation for all updates
- Optimized for large datasets
- Returns detailed statistics about the operation

## Usage Example

### cURL
```bash
curl -X POST \
  http://localhost:3000/admin/room/bulk-update-iso-codes \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json'
```

### JavaScript/Node.js
```javascript
const axios = require('axios');

const response = await axios.post(
  'http://localhost:3000/admin/room/bulk-update-iso-codes',
  {},
  {
    headers: {
      'Authorization': 'Bearer YOUR_JWT_TOKEN',
      'Content-Type': 'application/json'
    }
  }
);

console.log(`Updated ${response.data.data.updated_count} rooms`);
```

## Error Handling
The API returns appropriate error responses:
- 401: Unauthorized (invalid/missing token)
- 500: Server error with detailed error message

## Database Impact
- Updates rooms with exact name matches to their corresponding ISO codes from `room_iso_codes`
- Sets ISO code to '999' for rooms without matching names in `room_iso_codes`
- Ensures all rooms have an ISO code after the operation
- Safe to run multiple times (idempotent operation)
