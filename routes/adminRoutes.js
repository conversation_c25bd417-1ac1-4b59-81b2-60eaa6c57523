const express = require("express");
const router = express();
const adminController = require("../controllers/Admin/commonController");
const commonController = require("../controllers/Admin/commonController");
const companyController = require("../controllers/Admin/companyController");
const staffController = require("../controllers/Admin/staffController");
const qrController = require("../controllers/Admin/qrCodeController");
const customerController = require("../controllers/Admin/customerController");
const roomController = require("../controllers/Admin/roomController");
const itemSuggestionController = require("../controllers/Admin/itemSuggestionController");
const shipmentTypeController = require("../controllers/Admin/shipmentTypeController");
const shipmentTypeForShipmentController = require("../controllers/Admin/shipmentTypeForShipmentController");
const inventoryController = require("../controllers/Admin/inventoryController");
const shipmentController = require("../controllers/Admin/shipmentController");
const homeController = require("../controllers/APP/homeController");
const tagController = require("../controllers/Admin/tagController");
const genericController = require("../controllers/Admin/genericController");
const groupController = require("../controllers/Admin/groupController");
const authentication = require("../middlewares/authentication");
const validators = require("../assets/validators");
const multer = require("multer");
const { errorHandler } = require("../assets/common");
const upload = multer({ dest: "temp/" });

/**
 * @desc Api for Google Get Place Details.
 * @method POST
 */

router
	.route("/google/place/details")
	.post(
		errorHandler,
		commonController.getPlaceDetailsController
	);

/**
 * @desc Api for generate multiple shipment using script.
 * @method POST
 */

router
	.route("/script/shipment/add")
	.post(
		validators.admin.scriptShipmentController,
		errorHandler,
		customerController.isValidCustomerController,
		shipmentTypeController.isValidShipmentTypeController,
		companyController.isValidCompanyController,
		shipmentController.scriptShipmentController
	);

/**
* @desc Api for generate multiple item for shipment using script.
* @method POST
*/

router
	.route("/script/item/add")
	.post(
		upload.fields([
			{ name: "photo" },
			{ name: "photo1" },
		]),
		homeController.scriptAddItemController
	);

/**
* @desc Api for generate shipments shipment type copy using script
* @method POST
*/

router
	.route("/script/shipment/shipment-type/copy")
	.post(
		authentication.validateToken,
		upload.none(),
		shipmentTypeForShipmentController.copyShipmentStagesForShipment
	);

/**
* @desc Api for generate shipmentInventory shipment stage copy using script
* @method POST
*/

router
	.route("/script/shipmentInventory/shipment-stage/copy")
	.post(
		authentication.validateToken,
		upload.none(),
		shipmentTypeForShipmentController.copyisSccanedStageForShipmentInventory
	);

/**
* @desc Api for generate shipmentInventory shipment override copy using script
* @method POST
*/

router
	.route("/script/shipmentInventory/shipment-stage/copy/override")
	.post(
		authentication.validateToken,
		upload.none(),
		shipmentTypeForShipmentController.copyIsOverrideStageForShipmentInventory
	);

/**
* @desc Api for generate shipment forced copy using script
* @method POST
*/

router
	.route("/script/shipment/forced/copy")
	.post(
		authentication.validateToken,
		upload.none(),
		shipmentTypeForShipmentController.copyShipmentForcedDetails
	);



/**
* @desc Api for generate shipment forced copy using script
* @method POST
*/

router
	.route("/script/shipment/signature/copy")
	.post(
		authentication.validateToken,
		upload.none(),
		shipmentTypeForShipmentController.copyShipmentSignatureDetails
	);

/**
 * @desc Api for company/staff sign in.
 * @method POST
 */

router
	.route("/signin")
	.post(
		upload.none(),
		validators.admin.adminSignIn,
		errorHandler,
		adminController.signIn
	);

/**
* @desc Api for company/staff sign in.
* @method POST
*/

router
	.route("/signin-with-companyId")
	.post(
		upload.none(),
		validators.admin.adminSignInWithCompanyId,
		errorHandler,
		adminController.adminSignInWithCompanyId
	);


/**
* @desc Api for company/staff/superadmin notification status change.
* @method POST
*/

router
	.route("/notificationStatusChange")
	.post(
		upload.none(),
		adminController.notificationStatusChange
	);

/**
* @desc Api for company/staff change password.
* @method POST
*/

router.post(
	"/change-password",
	upload.none(),
	validators.admin.viewEmail,
	validators.admin.changePassword,
	errorHandler,
	authentication.validateToken,
	adminController.adminChangePassword
);

/**
* @desc Api for company/staff forgot password.
* @method POST
*/

router.post(
	"/forgot-password",
	upload.none(),
	validators.admin.viewEmail,
	errorHandler,
	adminController.adminForgotPassword
);

/**
* @desc Api for company/staff reset password.
* @method POST
*/

router.post(
	"/reset-password",
	upload.none(),
	validators.admin.resetPassword,
	errorHandler,
	adminController.adminResetPassword
);


/**
* @desc Api for get dashbord info.
* @method POST
*/

router
	.route("/dashboard")
	.post(
		upload.none(),
		authentication.validateToken,
		errorHandler,
		commonController.dashboard
	);

/**
* @desc Api for generate company.
* @method POST
*/

router
	.route("/company/add")
	.post(
		authentication.validateToken,
		upload.single("photo"),
		validators.admin.addCompany,
		errorHandler,
		companyController.addCompany
	);


/**
* @desc Api for create units Company 
* @method POST
*/

router
	.route("/company/addUnit")
	.post(
		authentication.validateToken,
		upload.none(),
		companyController.addCompanyUnits
	);

/**
* @desc Api for create units Company 
* @method POST
*/

router
	.route("/company/newCompany/fetchUnits")
	.post(
		authentication.validateToken,
		upload.none(),
		companyController.fetchNewCompanyUnits
	);

/**
* @desc Api for get fetchExistingCompanyCustomerData for company.
* @method POST
*/

router.route("/fetchExistingCompanyCustomerData")
	.post(
		authentication.validateToken,
		upload.none(),
		companyController.fetchExistingCompanyCustomerData
	);

/**
* @desc Api for get fetchExistingCompanyStaffData for company.
* @method POST
*/

router.route("/fetchExistingCompanyStaffData")
	.post(
		authentication.validateToken,
		upload.none(),
		companyController.fetchExistingCompanyStaffData
	);

/**
* @desc Api for generate storageId for company.
* @method POST
*/

router
	.route("/company/storageId/update")
	.post(
		authentication.validateToken,
		companyController.companyStorageIdUpdate
	);

/**
* @desc Api for generate storageId for staff.
* @method POST
*/

router
	.route("/staff/storageId/update")
	.post(
		authentication.validateToken,
		staffController.staffStorageIdUpdate
	);

/**
* @desc Api for generate storageId for staff by Email.
* @method POST
*/

router
	.route("/staff/storageId/updateByEmail")
	.post(
		authentication.validateToken,
		staffController.staffStorageIdUpdateByEmail
	);

/**
* @desc Api for generate storageId for customer.
* @method POST
*/

router
	.route("/shipment/storageId/update")
	.post(
		authentication.validateToken,
		shipmentController.shipmentStorageIdUpdate
	);

/**
* @desc Api for generate storageId for customer.
* @method POST
*/

router
	.route("/customer/storageId/update")
	.post(
		authentication.validateToken,
		customerController.customerStorageIdUpdate
	);

/**
* @desc Api for fetch company list.
* @method POST
*/

router
	.route("/company/list")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.getCompanyList,
		errorHandler,
		companyController.getCompanyList
	);

/**
* @desc Api for check company account id mandatory.
* @method POST
*/

router
	.route("/check/account-id-mandatory")
	.get(
		authentication.validateToken,
		companyController.checkCompanyAccountIdMandatory
	);


/**
* @desc Api for change integration key status
* @method POST
*/

router
	.route("/change-integration_key_status")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.viewCompany,
		errorHandler,
		companyController.changeIntegrationKeyStatusController
	);


/**
* @desc Api for fetch company Api-key for user and company.
* @method POST
*/

router
	.route("/integrationKey/fetch")
	.get(
		authentication.validateToken,
		upload.none(),
		companyController.getIntegrationKeyController
	);

/**
* @desc Api for view company.
* @method POST
*/

router
	.route("/company/view-company")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.viewCompany,
		companyController.viewCompany
	);

/**
* @desc Api for view company using companyId.
* @method POST
*/

router
	.route("/company/view-company/:companyId")
	.get(
		authentication.validateToken,
		upload.none(),
		validators.admin.viewCompanyGet,
		errorHandler,
		companyController.viewCompany
	);

/**
* @desc Api for edit company.
* @method POST
*/

router
	.route("/company/edit-company")
	.post(
		authentication.validateToken,
		upload.single("photo"),
		validators.admin.editCompany,
		errorHandler,
		companyController.editCompany
	);

/**
* @desc Api for edit company for storage.
* @method POST
*/

router
	.route("/company/edit-company-storage")
	.post(
		authentication.validateToken,
		upload.single("photo"),
		validators.admin.editCompanyStorage,
		errorHandler,
		companyController.editCompanyStorage
	);

/**
* @desc Api for change company status.
* @method POST
*/

router
	.route("/company/change-company-status")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.changeCompanyStatus,
		errorHandler,
		companyController.changeCompanyStatus
	);

/**
* @desc Api for change company status for storage.
* @method POST
*/

router
	.route("/company/change-company-status-storage")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.changeCompanyStatusStorage,
		errorHandler,
		companyController.changeCompanyStatusStorage
	);

/**
* @desc Api for delete company.
* @method POST
*/

router
	.route("/company/delete")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.deleteCompany,
		errorHandler,
		companyController.deleteCompany
	);

/**
* @desc Api for fetch customer list.
* @method GET
*/

router
	.route("/customer/list")
	.get(
		authentication.validateToken,
		upload.none(),
		validators.admin.getCustomerList,
		errorHandler,
		customerController.getCustomerList
	);

/**
* @desc Api for create customer using CSV.
* @method POST
*/

router
	.route("/add-csv-customer")
	.post(
		authentication.validateToken,
		customerController.addCsvCustomer
	);

/**
* @desc Api for generate customer.
* @method POST
*/

router
	.route("/customer/add")
	.post(
		authentication.validateToken,
		upload.single("photo"),
		validators.admin.addCustomer,
		tagController.isValidTagController,
		errorHandler,
		customerController.addCustomer
	);

/**
* @desc Api for generate customer for storage.
* @method POST
*/

router
	.route("/customer/add-storage")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.addCustomerStorage,
		errorHandler,
		customerController.addCustomerStorage
	);

/**
* @desc Api for check customer for already exits.
* @method POST
*/

router
	.route("/customer/already-check-storage")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.alreadyCustomeCheckStorage,
		errorHandler,
		customerController.alreadyCustomeCheckStorage
	);

/**
* @desc Api for check customer for already exits edit.
* @method POST
*/

router
	.route("/customer/already-check-storage-edit")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.alreadyCustomeCheckStorageEdit,
		errorHandler,
		customerController.alreadyCustomeCheckStorageEdit
	);

/**
* @desc Api for view customer.
* @method POST
*/

router
	.route("/customer/view-customer")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.viewCustomer,
		errorHandler,
		customerController.viewCustomer
	);

/**
* @desc Api for edit customer.
* @method POST
*/

router
	.route("/customer/edit-customer")
	.post(
		authentication.validateToken,
		upload.single("photo"),
		validators.admin.editCustomer,
		tagController.isValidTagController,
		errorHandler,
		customerController.isValidCustomerController,
		customerController.editCustomer
	);


/**
* @desc Api for edit customer for storage.
* @method POST
*/

router
	.route("/customer/edit-customer-storage")
	.post(
		authentication.validateToken,
		upload.single("photo"),
		validators.admin.editCustomerStorage,
		errorHandler,
		customerController.isValidCustomerControllerStorage,
		customerController.editCustomer
	);

/**
* @desc Api for delete customer.
* @method POST
*/

router
	.route("/customer/delete")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.deleteCustomer,
		errorHandler,
		customerController.checkCustomerAssignToJob,
		customerController.deleteCustomer
	);

/**
* @desc Api for change customer status.
* @method POST
*/

router
	.route("/customer/change-customer-status")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.changeCustomerStatus,
		errorHandler,
		customerController.checkCustomerIsValidToUpdateStatus,
		customerController.changeCustomerStatus
	);

/**
* @desc Api for change customer status for storage.
* @method POST
*/

router
	.route("/customer/change-customer-status-storage")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.changeCustomerStatusStorage,
		errorHandler,
		customerController.changeCustomerStatusStorage
	);

/**
* @desc Api for staff reset password.
* @method POST
*/

router.post(
	"/staff/reset-password",
	upload.none(),
	validators.admin.resetPassword,
	errorHandler,
	staffController.resetPassword
);

/**
* @desc Api for company reset password.
* @method POST
*/

router.post(
	"/cms/reset-password",
	upload.none(),
	validators.admin.resetPassword,
	errorHandler,
	adminController.cmsResetPassword
);

/**
* @desc Api for company reset password by super admin.
* @method POST
*/

router.post(
	"/cms/update-company-password",
	upload.none(),
	validators.admin.superAdminCompanyUpdatePassword,
	errorHandler,
	adminController.superAdminCompanyUpdatePassword
);

/**
* @desc Api for generate staff.
* @method POST
*/

router
	.route("/staff/add")
	.post(
		authentication.validateToken,
		upload.single("photo"),
		validators.admin.addStaff,
		errorHandler,
		staffController.addStaff
	);

/**
* @desc Api for generate staff by Storage.
* @method POST
*/

router
	.route("/staff/add-storage")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.addStaffStorage,
		errorHandler,
		staffController.addStaffStorage
	);

/**
* @desc Api for fetch staff list.
* @method POST
*/

router
	.route("/staff/list")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.getStaffList,
		errorHandler,
		staffController.getStaffList
	);

/**
* @desc Api for fetch all staff list.
* @method POST
*/

router
	.route("/staff/listAllUsers")
	.post(
		authentication.validateToken,
		staffController.getlistAllUsers
	);


/**
* @desc Api for view staff.
* @method POST
*/

router
	.route("/staff/view-staff")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.viewStaff,
		errorHandler,
		staffController.viewStaff
	);

/**
* @desc Api for edit staff.
* @method POST
*/

router
	.route("/staff/edit-staff")
	.post(
		authentication.validateToken,
		upload.single("photo"),
		validators.admin.editStaff,
		errorHandler,
		staffController.editStaff
	);


/**
* @desc Api for edit staff.
* @method POST
*/

router
	.route("/staff/edit-staff-storage")
	.post(
		authentication.validateToken,
		upload.single("photo"),
		validators.admin.editStaffStorage,
		errorHandler,
		staffController.editStaffStorage
	);

/**
* @desc Api for change staff status.
* @method POST
*/

router
	.route("/staff/change-staff-status")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.changeStaffStatus,
		errorHandler,
		staffController.changeStaffStatus
	);

/**
* @desc Api for change staff status for storage.
* @method POST
*/

router
	.route("/staff/change-staff-status-storage")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.changeStaffStatusStorage,
		errorHandler,
		staffController.changeStaffStatusStorage
	);

/**
* @desc Api for check valid delete staff.
* @method POST
*/

router
	.route("/staff/ValidToDelete")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.deleteStaff,
		errorHandler,
		staffController.checkStaffAssignToJob,
	);

/**
* @desc Api for delete staff.
* @method POST
*/

router
	.route("/staff/delete")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.deleteStaff,
		errorHandler,
		staffController.deleteStaff
	);


/**
* @desc Api for staff to get company listing.
* @method POST
*/

router
	.route("/staff/view-company-list")
	.post(
		authentication.validateToken,
		upload.none(),
		staffController.viewCompanyList
	);

/**
* @desc Api for staff to get company listing.
* @method POST
*/

router
	.route("/shipment-type/add")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.addShipmentType,
		errorHandler,
		shipmentTypeController.addShipmentType
	);


/**
* @desc Api for add shipment type stgae.
* @method POST
*/

router
	.route("/shipment-type-stage/add")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.addShipmentTypeStage,
		errorHandler,
		shipmentTypeController.addShipmentTypeStage
	);



/**
* @desc Api for fetch shipment-type list.
* @method POST
*/

router
	.route("/shipment-type/list")
	.post(
		authentication.validateToken,
		upload.none(),
		shipmentTypeController.listShipmentType
	);

/**
* @desc Api for view shipment-type stages list.
* @method POST
*/

router
	.route("/shipment-type/view-shipment-stage-list")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.listShipmentTypeStage,
		errorHandler,
		shipmentTypeController.listShipmentTypeStage
	);

/**
* @desc Api for edit shipment-type.
* @method POST
*/

router
	.route("/shipment-type/edit")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.editShipmentType,
		errorHandler,
		shipmentTypeController.editShipmentType
	);

/**
* @desc Api for fetch shipment-type status.
* @method POST
*/

router
	.route("/shipment-type/status")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.statusShipmentType,
		errorHandler,
		shipmentTypeController.checkShipmentTypeAssignToInactiveJob,
		shipmentTypeController.statusShipmentType
	);

/**
* @desc Api for delete shipment-type.
* @method POST
*/

router
	.route("/shipment-type/delete")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.deleteShipmentType,
		errorHandler,
		shipmentTypeController.checkShipmentTypeAssignToJob,
		shipmentTypeController.deleteShipmentType
	);

/**
* @desc Api for change stage status for shipment-type.
* @method POST
*/

router
	.route("/shipment-type/change-shipment-stage-status")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.changeShipmentStageStatus,
		errorHandler,
		shipmentTypeController.changeShipmentStageStatus
	);

/**
* @desc Api for view shipment-type.
* @method POST
*/

router
	.route("/shipment-type/view-shipment-type")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.viewShipmentType,
		errorHandler,
		shipmentTypeController.viewShipmentType
	);


/**
* @desc Api for view shipment-type stage.
* @method POST
*/

router
	.route("/shipment-type/view-shipment-stage")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.viewShipmentStage,
		errorHandler,
		shipmentTypeController.viewShipmentStage
	);

/**
* @desc Api for view shipment-type-for-shipment stage.
* @method POST
*/

router
	.route("/shipment-type-for-shipment/view-shipment-stage")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.viewShipmentForShipmentStage,
		errorHandler,
		shipmentTypeForShipmentController.viewShipmentForShipmentStage
	);


/**
* @desc Api for view shipment-type stage.
* @method POST
*/

router
	.route("/shipment-type/edit-shipment-stage")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.editShipmentStage,
		errorHandler,
		shipmentTypeController.editShipmentStage
	);


/**
* @desc Api for view shipment-type-for-shipment stage.
* @method POST
*/

router
	.route("/shipment-type-for-shipment/edit-shipment-stage")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.editShipmentStageForShipment,
		errorHandler,
		shipmentTypeForShipmentController.editShipmentStage
	);

/**
* @desc Api for generate generic-labels.
* @method POST
*/

router
	.route("/generic_label/generate")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.genericLabelGenerate,
		errorHandler,
		genericController.generateLabelController
	);



/**
* @desc Api for fetch generic-labels list.
* @method GET
*/

router
	.route("/generic_label/list/:batchId")
	.get(
		authentication.validateToken,
		upload.none(),
		validators.admin.labelGenerateList,
		errorHandler,
		genericController.listLabelController
	);


/**
* @desc Api for GET batch list.
* @method GET
*/

router
	.route("/batch/list")
	.get(
		authentication.validateToken,
		genericController.listBatchController
	);

/**
* @desc Api for GET last generic label list for company.
* @method GET
*/

router
	.route("/last/genericLabel")
	.get(
		authentication.validateToken,
		genericController.findCompanyLastGenericLabelController
	);

/**
* @desc Api for delete batch using batchId.
* @method POST
*/

router
	.route("/batch/delete/:batchId")
	.post(
		authentication.validateToken,
		genericController.checkAssignToItems,
		genericController.deleteBatchController

	);

router.route("/generic_label/:qrCodeId")
	.delete(
		authentication.validateToken,
		upload.none(),
		validators.admin.genericLabelDelete,
		errorHandler,
		genericController.deleteQrLabelController
	);

/**
* @desc Api for print generic dymo label .
* @method POST
*/

router.
	route("/generic_label/dymo/print/:qrCodeId")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.genericLabelDelete,
		errorHandler,
		genericController.printDymoLabelController
	);

/**
* @desc Api for print generic label qr pdf.
* @method POST
*/

router
	.route("/generic_label/:jobId/print/:batchId")
	.post(
		authentication.validateToken,
		upload.none(),
		// validators.admin.qrCodeCompanyList,
		errorHandler,
		genericController.printLabelController
	);

/**
* @desc Api for print singal generic label qr pdf.
* @method POST
*/

router
	.route("/generic_label/:jobId/print")
	.post(
		authentication.validateToken,
		upload.none(),
		errorHandler,
		genericController.printSingalLabelController
	);

/**
* @desc Api for generate qrcode.
* @method POST
*/

router
	.route("/qr_code/generate")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.qrCodeGenerate,
		errorHandler,
		qrController.checkJobExistence,
		qrController.generateQrController
	);

/**
* @desc Api for delete qrcode using qrCodeId.
* @method DELETE
*/

router.route("/qr_code/:qrCodeId")
	.delete(
		authentication.validateToken,
		upload.none(),
		validators.admin.qrCodeDelete,
		errorHandler,
		qrController.deleteQrController
	);

/**
* @desc Api for print qrcode pdf.
* @method POST
*/

router
	.route("/qr_code/:jobId/print")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.qrCodeList,
		errorHandler,
		qrController.checkJobExistence,
		qrController.printLabelController
	);


/**
* @desc Api for print singal qrcode pdf.
* @method POST
*/

router
	.route("/qr_code/:qrId/singalQrPrint")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.singalQrCodeList,
		errorHandler,
		qrController.checksingalQrCodeExistence,
		qrController.singalQrPrintLabelController
	);

/**
* @desc Api for fetch qrCode list.
* @method GET
*/

router
	.route("/qr_code/:jobId/list")
	.get(
		authentication.validateToken,
		upload.none(),
		validators.admin.qrCodeList,
		errorHandler,
		qrController.checkJobExistence,
		qrController.listQrCodeController
	);

/**
* @desc Api for fetch qrCode dymo list.
* @method GET
*/

router
	.route("/qr_code/:jobId/dymo-list")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.qrCodeList,
		errorHandler,
		qrController.checkJobExistence,
		qrController.listDymoQrCodeController
	);

/**
* @desc Api for fetch qrCode settings.
* @method GET
*/

router
	.route("/qr_code/:jobId/get_setting")
	.get(
		authentication.validateToken,
		upload.none(),
		validators.admin.qrCodeList,
		errorHandler,
		qrController.checkJobExistence,
		qrController.findQrSettingsController
	);

/**
* @desc Api for fetch qrCode first and last labels.
* @method GET
*/

router
	.route("/qr_code/:jobId/get_qr_first_and_last_label")
	.get(
		authentication.validateToken,
		upload.none(),
		validators.admin.qrCodeList,
		errorHandler,
		qrController.checkJobExistence,
		qrController.findQrFirstAndLastLabelController
	);

/**
* @desc Api for save qrcode settings.
* @method POST
*/

router
	.route("/qr_code/:jobId/save_setting")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.qrCodeList,
		errorHandler,
		qrController.checkJobExistence,
		qrController.saveQrSettings
	);

/**
* @desc Api for fetch inventory lists.
* @method POST
*/

router
	.route("/inventory/list")
	.post(
		authentication.validateToken,
		upload.none(),
		inventoryController.listInventory
	);

/**
* @desc Api for get staff using staff type.
* @method GET
*/

router
	.route("/staff/basic/:staff_type")
	.get(
		authentication.validateToken,
		upload.none(),
		validators.admin.staffBasic,
		errorHandler,
		staffController.staffBasicController
	);

/**
* @desc Api for get staff using staff type.
* @method GET
*/

router
	.route("/shipment-type/basic")
	.get(
		authentication.validateToken,
		upload.none(),
		shipmentTypeController.basicShipmentTypeController
	);

/**
* @desc Api for get shipment type list for storage.
* @method GET
*/

router
	.route("/shipment-type-storage/list")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.viewCompanyStorage,
		errorHandler,
		shipmentTypeController.ShipmentTypeListStorageController
	);

/**
* @desc Api for get shipment stage.
* @method GET
*/

router
	.route("/shipment_stage/basic")
	.get(
		authentication.validateToken,
		upload.none(),
		shipmentController.basicShipmentTypeController
	);

/**
* @desc Api for get customer.
* @method GET
*/

router
	.route("/customer/basic")
	.get(
		authentication.validateToken,
		upload.none(),
		customerController.customerBasicController
	);


/**
* @desc Api for get companyId.
* @method GET
*/

router
	.route("/companyID/basic")
	.get(
		upload.none(),
		companyController.companyIDBasicController
	);

/**
* @desc Api for get staff list using shipmentId.
* @method POST
*/

router
	.route("/staff/list/:shipmentId")
	.post(
		authentication.validateToken,
		validators.admin.shipmentFetchDetail,
		errorHandler,
		staffController.staffListByShipmentController
	);

/** 
*@desc Api for get staff list using company.
* @method POST
*/

router
	.route("/staff/list-company/:shipmentId")
	.post(
		authentication.validateToken,
		validators.admin.shipmentFetchDetail,
		errorHandler,
		staffController.staffListByShipmentCompanyController
	);

/**
* @desc Api for delete tag using tagId.
* @method DELETE
*/

router
	.route("/staff/delete-assign-worker-stage")
	.post(
		upload.none(),
		authentication.validateToken,
		staffController.removeStaffIdController
	);


/**
* @desc Api for get staff list using companyId.
* @method POST
*/

router
	.route("/staff/list-shipment-type")
	.post(
		authentication.validateToken,
		errorHandler,
		staffController.staffListByCompanyController
	);

/**
* @desc Api for get shipment type stages for shipment
* @method POST
*/

router
	.route("/assign-shipment-type-stages")
	.post(
		authentication.validateToken,
		validators.admin.shipmentCheck,
		errorHandler,
		shipmentController.getAssignShipmentTypeStages
	);



/**
* @desc Api for generate shipment.
* @method POST
*/

router
	.route("/shipment/")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.shipmentCreate,
		validators.admin.viewCompany,
		validators.admin.viewCustomer,
		errorHandler,
		customerController.isValidCustomerController,
		shipmentTypeController.isValidShipmentTypeController,
		shipmentController.isPickupDateMandatoryCheck,
		tagController.isValidTagController,
		companyController.isValidCompanyController,
		shipmentController.createShipmentController
	);


/**
* @desc Api for generate shipment.
* @method POST
*/

router
	.route("/shipment-storage/")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.shipmentCreateStorage,
		validators.admin.viewCompanyStorage,
		validators.admin.viewCustomerStorage,
		errorHandler,
		shipmentController.isStorageShipmentExitsController,
		customerController.isValidCustomerControllerStorage,
		shipmentTypeController.isValidShipmentTypeController,
		companyController.isValidCompanyController,
		shipmentController.createShipmentController
	);
/**
* @desc Api for generate shipment pdf.
* @method POST
*/

router
	.route("/shipment/pdf")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.shipmentCheck,
		errorHandler,
		shipmentController.isCheckValidShipmentforItemsController,
		shipmentController.downloadPdfController
	);

/**
* @desc Api for generate shipment pdf for customer.
* @method POST
*/

router
	.route("/shipment/pdf/customer")
	.post(
		upload.none(),
		validators.admin.shipmentCheck,
		errorHandler,
		shipmentController.isCheckValidShipmentforItemsController,
		shipmentController.downloadPdfController
	);
/**
* @desc Api for generate shipment pdf.
* @method POST
*/

router
	.route("/customer/pdf")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.shipmentCheck,
		validators.admin.viewCustomer,
		errorHandler,
		shipmentController.customerPdfController
	);

/**
* @desc Api for add Documents using shipmentId.
* @method POST
*/

router
	.route("/shipment/add/documents/:shipmentId")
	.post(
		authentication.validateToken,
		upload.fields([
			{ name: "file" },
		]),
		shipmentController.isValidShipmentController,
		shipmentController.addShipmentDocumentsController
	);

/**
* @desc Api for delete Documents using shipmentId.
* @method POST
*/

router
	.route("/shipment/delete/documents/:shipmentId")
	.delete(
		authentication.validateToken,
		upload.none(),
		shipmentController.isValidShipmentController,
		shipmentController.deleteShipmentDocumentsController
	);


/**
* @desc Api for generate shipment export excel file.
* @method POST
*/

router
	.route("/shipment/xlsx/export")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.shipmentCheck,
		errorHandler,
		shipmentController.isCheckValidShipmentforItemsController,
		shipmentController.xlsxExportShipmentInventoryController
	);

/**
* @desc Api for generate shipment export csv file.
* @method POST
*/

router
	.route("/shipment/csv/export")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.shipmentCheck,
		errorHandler,
		shipmentController.isCheckValidShipmentforItemsController,
		shipmentController.csvExportShipmentInventoryController
	);


/**
* @desc Api for generate shipment picklistPdf.
* @method POST
*/

router
	.route("/shipment/picklistPdf")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.shipmentCheck,
		errorHandler,
		shipmentController.isCheckValidShipmentforItemsController,
		shipmentController.downloadPicklistPdfController
	);

/**
* @desc Api for fetch shipment list.
* @method GET
*/

router
	.route("/shipment/list")
	.get(
		authentication.validateToken,
		validators.admin.shipmentList,
		errorHandler,
		shipmentController.listShipmentController
	);

/**
* @desc Api for fetch shipment.
* @method GET
*/

router
	.route("/shipment/basic")
	.get(
		authentication.validateToken,
		shipmentController.basicShipmentController
	);


/**
* @desc Api for fetch shipment using shipmentId for storgae.
* @method GET
*/

router
	.route("/fetchShipmentForStorage/:shipmentId")
	.get(
		authentication.validateToken,
		upload.none(),
		validators.admin.shipmentFetchDetail,
		shipmentController.fetchShipmentForStorageController
	);

/**
* @desc Api for fetch shipment using shipmentId.
* @method GET
*/

router
	.route("/shipment/:shipmentId")
	.get(
		authentication.validateToken,
		upload.none(),
		validators.admin.shipmentFetchDetail,
		validators.admin.orderingField,
		errorHandler,
		shipmentController.isValidShipmentController,
		shipmentController.getShipmentDetailController
	);

/**
* @desc Api for delete shipment using shipmentId.
* @method DELETE
*/

router
	.route("/shipment/:shipmentId")
	.delete(
		authentication.validateToken,
		upload.none(),
		validators.admin.shipmentFetchDetail,
		errorHandler,
		shipmentController.isValidShipmentController,
		shipmentController.checkShipmentAssignToJob,
		shipmentController.removeShipmentController
	);

/**
* @desc Api for delete shipment type stage using stageId.
* @method DELETE
*/

router
	.route("/shipmentTypeStage/:stageId")
	.post(
		authentication.validateToken,
		upload.none(),
		shipmentController.isItemsAssignToShipmentTypeStageController,
		shipmentController.removeShipmentTypeStageController
	);

/**
* @desc Api for update shipment using shipmentId.
* @method PUT
*/

router
	.route("/shipment/:shipmentId")
	.put(
		authentication.validateToken,
		upload.none(),
		validators.admin.shipmentUpdate,
		errorHandler,
		shipmentController.isValidShipmentController,
		shipmentController.isActiveCustomerForShipmentController,
		shipmentController.updateWarehouseController,
		shipmentController.updateShipmentController
	);

/**
* @desc Api for assign worker for shipment.
* @method PUT
*/

router
	.route("/shipment/:shipmentId/link/")
	.put(
		authentication.validateToken,
		upload.none(),
		validators.admin.shipmentFetchDetail,
		validators.admin.linkWorkers,
		errorHandler,
		staffController.isValidStaffIdController,
		shipmentController.isValidShipmentController,
		shipmentController.assignWorkerShipmentController
	);

/**
* @desc Api for assign worker for shipment.
* @method POST
*/

router
	.route("/staff/assign-to-shipment/:shipmentId")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.shipmentFetchDetail,
		validators.admin.linkWorkers,
		errorHandler,
		staffController.isValidStaffIdController,
		shipmentController.isValidShipmentController,
		shipmentController.staffAssignToShipmentStage
	);


/**
* @desc Api for assign worker for shipment.
* @method PUT
*/

router
	.route("/shipment/:shipmentId/unlink/")
	.put(
		authentication.validateToken,
		upload.none(),
		validators.admin.shipmentFetchDetail,
		validators.admin.unlinkWorkers,
		errorHandler,
		shipmentController.isValidShipmentController,
		shipmentController.unlinkWorkerShipmentController
	);

/**
* @desc Api for force stage change.
* @method PUT
*/

// router
// 	.route("/shipment/force_stage_change/:shipmentId/")
// 	.put(
// 		authentication.validateToken,
// 		upload.none(),
// 		validators.admin.shipmentFetchDetail,
// 		validators.admin.forceStageChange,
// 		errorHandler,
// 		shipmentController.isValidShipmentController,
// 		shipmentController.isValidShipmentItemAndWorkerController,
// 		shipmentTypeController.isValidShipmentStageController,
// 		shipmentController.forceShipmentStageController
// 	);


/**
* @desc Api for force stage change after stage add.
* @method PUT
*/

router
	.route("/shipment/force_stage_change/:shipmentId/")
	.put(
		authentication.validateToken,
		upload.none(),
		validators.admin.shipmentFetchDetail,
		validators.admin.forceStageChange,
		errorHandler,
		shipmentController.isValidShipmentController,
		shipmentController.isValidShipmentItemAndWorkerController,
		shipmentTypeController.isValidShipmentStageController,
		shipmentController.forceShipmentStageController
	);

/**
* @desc Api for fetch inventory using inventoryId.
* @method GET
*/

router
	.route("/inventory/:inventoryId")
	.get(
		authentication.validateToken,
		upload.none(),
		validators.admin.inventoryDetail,
		errorHandler,
		homeController.isValidInventoryController,
		homeController.getInventoryDetailController
	);


/**
* @desc Api for get customer using shipmentId.
* @method GET
*/

router
	.route("/customer/:shipmentId/job")
	.get(
		validators.admin.shipmentFetchDetail,
		validators.admin.orderingField,
		errorHandler,
		shipmentController.isValidShipmentController,
		customerController.updateCustomerMailStatusController,
		shipmentController.getShipmentDetailForCustomerController
	);


/**
* @desc Api for customer portal invite.
* @method POST
*/

router
	.route("/mail_customer/:shipmentId")
	.post(
		authentication.validateToken,
		validators.admin.shipmentFetchDetail,
		errorHandler,
		shipmentController.isValidShipmentController,
		customerController.mailShipmentDetailToCustomerController,
		customerController.updateCustomerMailStatusController
	);


/**
* @desc Api for print shipment summary.
* @method POST
*/

router
	.route("/shipment/:shipmentId/print_summary/")
	.post(
		authentication.validateToken,
		validators.admin.shipmentFetchDetail,
		errorHandler,
		shipmentController.isValidShipmentController,
		shipmentController.printJobSummaryController
	);

/**
* @desc Api for fetch room list.
* @method GET
*/

router
	.route("/room/list/")
	.get(
		authentication.validateToken,
		upload.none(),
		validators.admin.roomList,
		errorHandler,
		roomController.getRoomListingController
	);

/**
* @desc Api for fetch customer room list.
* @method GET
*/

router
	.route("/customer_room/list/")
	.get(
		upload.none(),
		validators.admin.roomList,
		errorHandler,
		roomController.getRoomListingController
	);

/**
* @desc Api for upadte room using roomId.
* @method PUT
*/

router
	.route("/room/:roomId/")
	.put(
		upload.none(),
		authentication.validateToken,
		validators.admin.viewRoom,
		validators.admin.roomName,
		errorHandler,
		roomController.isValidRoomController,
		roomController.editRoomController
	);

/**
* @desc Api for change room status.
* @method POST
*/

router
	.route("/room/change-room-status")
	.post(
		upload.none(),
		authentication.validateToken,
		validators.admin.changeShipmentRoomStatus,
		errorHandler,
		roomController.isValidRoomController,
		roomController.checkRoomAssignToJob,
		roomController.changeShipmentRoomStatus
	);

/**
* @desc Api for delete room using roomId.
* @method DELETE
*/

router
	.route("/room/:roomId/")
	.delete(
		upload.none(),
		authentication.validateToken,
		validators.admin.viewRoom,
		errorHandler,
		roomController.isValidRoomController,
		roomController.checkRoomAssignToJob,
		roomController.removeRoomController
	);

/**
* @desc Api for fetch room using roomId.
* @method GET
*/

router
	.route("/room/:roomId/")
	.get(
		upload.none(),
		authentication.validateToken,
		validators.admin.viewRoom,
		errorHandler,
		roomController.isValidRoomController,
		roomController.viewRoomController
	);


/**
* @desc Api for generate room.
* @method POST
*/

router
	.route("/room/")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.roomName,
		errorHandler,
		roomController.createRoomController
	);


/**
* @desc Api for tag list.
* @method GET
*/

router
	.route("/tag/list/")
	.get(
		authentication.validateToken,
		upload.none(),
		validators.admin.tagListData,
		errorHandler,
		tagController.getTagListingController
	);


/**
* @desc Api for fetch taglist for customer.
* @method GET
*/

router
	.route("/tag/list/customer")
	.get(
		authentication.validateToken,
		upload.none(),
		tagController.getTagForCustomerListingController
	);

/**
* @desc Api for fetch taglist for shipment.
* @method GET
*/

router
	.route("/tag/list/shipment")
	.get(
		authentication.validateToken,
		upload.none(),
		tagController.getTagForShipmentListingController
	);

/**
* @desc Api for update tag using tagId.
* @method PUT
*/

router
	.route("/tag/:tagId/")
	.put(
		upload.none(),
		authentication.validateToken,
		validators.admin.viewTag,
		validators.admin.tagEdit,
		errorHandler,
		tagController.isValidTagController,
		// companyController.isValidCompanyController,
		tagController.editTagController
	);

/**
* @desc Api for delete tag using tagId.
* @method DELETE
*/

router
	.route("/tag/:tagId/")
	.delete(
		upload.none(),
		authentication.validateToken,
		validators.admin.viewTag,
		errorHandler,
		tagController.isValidTagController,
		tagController.isTagAssignToItemController,
		tagController.removeTagController
	);

/**
* @desc Api for fetch tag using tagId.
* @method GET
*/

router
	.route("/tag/:tagId/")
	.get(
		upload.none(),
		authentication.validateToken,
		validators.admin.viewTag,
		errorHandler,
		tagController.isValidTagController,
		tagController.viewTagController
	);

/**
* @desc Api for generate tag.
* @method POST
*/

router
	.route("/tag/")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.tagAdd,
		errorHandler,
		tagController.createTagController
	);

/**
* @desc Api for fetch itemlist.
* @method GET
*/

router
	.route("/item_suggestion/list/")
	.get(
		authentication.validateToken,
		upload.none(),
		validators.admin.itemList,
		errorHandler,
		itemSuggestionController.getItemSuggestionListingController
	);

/**
* @desc Api for update item using itemId.
* @method PUT
*/

router
	.route("/item_suggestion/:itemId/")
	.put(
		upload.none(),
		authentication.validateToken,
		validators.admin.viewItem,
		validators.admin.itemName,
		errorHandler,
		itemSuggestionController.isValidItemSuggestionController,
		itemSuggestionController.editItemSuggestionController
	);

/**
* @desc Api for change item status.
* @method POST
*/

router
	.route("/item_suggestion/change-item-status")
	.post(
		upload.none(),
		authentication.validateToken,
		validators.admin.changeItemStatus,
		errorHandler,
		itemSuggestionController.isValidItemSuggestionController,
		itemSuggestionController.changeItemStatus
	);

/**
* @desc Api for delete item using itemId.
* @method DELETE
*/

router
	.route("/item_suggestion/:itemId/")
	.delete(
		upload.none(),
		authentication.validateToken,
		validators.admin.viewItem,
		errorHandler,
		itemSuggestionController.isValidItemSuggestionController,
		itemSuggestionController.removeItemController
	);

/**
* @desc Api for get item using itemId.
* @method GET
*/

router
	.route("/item_suggestion/:itemId/")
	.get(
		upload.none(),
		authentication.validateToken,
		validators.admin.viewItem,
		errorHandler,
		itemSuggestionController.isValidItemSuggestionController,
		itemSuggestionController.viewItemController
	);

/**
* @desc Api for generate item.
* @method POST
*/

router
	.route("/item_suggestion/")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.itemName,
		errorHandler,
		itemSuggestionController.createItemSuggestionController
	);


/**
* @desc Api for generate group.
* @method POST
*/

router
	.route("/add_group")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.groupName,
		errorHandler,
		groupController.isCheckGroupNameExistsController,
		groupController.createGroupController
	);



/**
* @desc Api for fetch groupList.
* @method GET
*/

router
	.route("/group/list")
	.get(
		authentication.validateToken,
		upload.none(),
		validators.admin.groupList,
		errorHandler,
		groupController.getGroupListingController
	);

/**
* @desc Api for fetch groupList.
* @method GET
*/

router
	.route("/group/user/list/:groupId")
	.get(
		authentication.validateToken,
		upload.none(),
		validators.admin.groupList,
		errorHandler,
		groupController.getGroupUserListingController
	);

/**
* @desc Api for delete group using groupId.
* @method DELETE
*/

router
	.route("/group/:groupId")
	.delete(
		upload.none(),
		authentication.validateToken,
		validators.admin.viewGroup,
		errorHandler,
		groupController.isValidGroupController,
		groupController.isGroupAssignController,
		groupController.removeGroupController
	);

/**
* @desc Api for delete group user using groupUserId.
* @method DELETE
*/

router
	.route("/groupUser/:groupUserId")
	.delete(
		upload.none(),
		authentication.validateToken,
		validators.admin.viewGroupUser,
		errorHandler,
		groupController.isValidGroupUserController,
		groupController.removeGroupUserController
	);


/**
* @desc Api for get group using groupId.
* @method GET
*/

router
	.route("/group/:groupId")
	.get(
		upload.none(),
		authentication.validateToken,
		validators.admin.viewGroup,
		errorHandler,
		groupController.isValidGroupController,
		groupController.viewGroupController
	);


/**
* @desc Api for update group using groupId.
* @method PUT
*/

router
	.route("/group/:groupId/")
	.put(
		upload.none(),
		authentication.validateToken,
		validators.admin.viewGroup,
		validators.admin.groupName,
		errorHandler,
		groupController.isValidGroupController,
		groupController.editGroupController
	);

/**
* @desc Api for update group using groupId.
* @method PUT
*/

router
	.route("/create/superAdminStaff")
	.post(
		upload.none(),
		authentication.validateToken,
		validators.admin.superAdminStaff,
		errorHandler,
		staffController.createSuperAdminStaffController
	);

/**
* @desc Api for staff upadte password.
* @method POST
*/

router
	.route("/staff/update-password")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.updatePassword,
		errorHandler,
		staffController.updatePassword
	);


/**
* @desc Api for change room status active using room list.
* @method POST
*/

router
	.route("/room/batch-room-list-status-change")
	.post(
		upload.none(),
		authentication.validateToken,
		validators.admin.batchRoomList,
		errorHandler,
		roomController.batchRoomListStatusChange
	);

/**
* @desc Api for change room delete using room list.
* @method POST
*/

router
	.route("/room/batch-delete-room-list")
	.post(
		upload.none(),
		authentication.validateToken,
		validators.admin.batchRoomList,
		errorHandler,
		roomController.batchDeleteRoomList
	);

/**
* @desc Api for bulk update room ISO codes by matching with room_iso_codes table.
* @method POST
*/

router
	.route("/room/bulk-update-iso-codes")
	.post(
		upload.none(),
		roomController.bulkUpdateRoomIsoCodes
	);

/**
* @desc Api for change item status using item list.
* @method POST
*/

router
	.route("/item/batch-item-list-status-change")
	.post(
		upload.none(),
		authentication.validateToken,
		validators.admin.batchItemList,
		errorHandler,
		itemSuggestionController.batchItemListStatusChange
	);

/**
* @desc Api for change item delete using item list.
* @method POST
*/

router
	.route("/item/batch-delete-item-list")
	.post(
		upload.none(),
		authentication.validateToken,
		validators.admin.batchItemList,
		errorHandler,
		itemSuggestionController.batchDeleteItemList
	);


router
	.route("/group-company-list")
	.post(
		upload.none(),
		validators.app.companyListByGroup,
		errorHandler,
		homeController.companyListByGroup,
	);

router
	.route("/super-admin-company-list")
	.post(
		upload.none(),
		validators.app.companyListSuperAdmin,
		errorHandler,
		homeController.companyListBySuperAdmin,
	);


module.exports = router;
