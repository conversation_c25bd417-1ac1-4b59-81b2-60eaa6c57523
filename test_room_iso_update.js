/**
 * Test script for bulk room ISO code update API
 * This script demonstrates how to use the new bulk update endpoint
 */

const axios = require('axios');

// Configuration
const API_BASE_URL = 'http://localhost:3000'; // Adjust as needed
const API_ENDPOINT = '/admin/room/bulk-update-iso-codes';

// Test function
async function testBulkRoomIsoUpdate() {
    try {
        console.log('🚀 Testing bulk room ISO code update...');
        
        // You'll need to replace this with a valid JWT token from your system
        const authToken = 'YOUR_JWT_TOKEN_HERE';
        
        const response = await axios.post(`${API_BASE_URL}${API_ENDPOINT}`, {}, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ Success!');
        console.log('📊 Response:', JSON.stringify(response.data, null, 2));
        
        if (response.data.data) {
            const { updated_count, statistics } = response.data.data;
            console.log(`\n📈 Summary:`);
            console.log(`   - Updated ${updated_count} room(s)`);
            console.log(`   - Before: ${statistics.before_update.rooms_with_iso_code} rooms had ISO codes`);
            console.log(`   - After: ${statistics.after_update.rooms_with_iso_code} rooms have ISO codes`);
            console.log(`   - Total rooms: ${statistics.after_update.total_shipment_rooms}`);
        }
        
    } catch (error) {
        console.error('❌ Error:', error.response?.data || error.message);
    }
}

// Run the test
if (require.main === module) {
    testBulkRoomIsoUpdate();
}

module.exports = { testBulkRoomIsoUpdate };
