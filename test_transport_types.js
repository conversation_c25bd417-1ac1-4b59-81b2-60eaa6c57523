/**
 * Test script for transport-types API
 * This script demonstrates how to test the transport-types endpoint
 */

const axios = require('axios');

// Configuration
const API_BASE_URL = 'http://localhost:3000'; // Adjust as needed
const API_ENDPOINT = '/admin/transport-types';

// Test function
async function testTransportTypes() {
    try {
        console.log('🚀 Testing transport-types API...');
        
        const response = await axios.get(`${API_BASE_URL}${API_ENDPOINT}`, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ Success!');
        console.log('📊 Response:', JSON.stringify(response.data, null, 2));
        
        if (response.data.data && Array.isArray(response.data.data)) {
            console.log(`\n📈 Summary:`);
            console.log(`   - Found ${response.data.data.length} transport type(s)`);
            
            response.data.data.forEach((transportType, index) => {
                console.log(`   ${index + 1}. ${transportType.name} (Code: ${transportType.code})`);
            });
        }
        
    } catch (error) {
        console.error('❌ Error:', error.response?.data || error.message);
        
        if (error.response?.data?.message?.includes('findAll')) {
            console.log('\n💡 This looks like the schema/table mismatch issue.');
            console.log('   The fix should be applied to database/schemas/transport-type-code.js');
            console.log('   Change "transport-type-codes" to "transport_type_codes" in the schema definition.');
        }
    }
}

// Run the test
if (require.main === module) {
    testTransportTypes();
}

module.exports = { testTransportTypes };
